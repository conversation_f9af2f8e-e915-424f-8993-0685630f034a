#!/usr/bin/env python3
"""
Debug script to test MapHandler zone loading
"""

import json
from enforcement.map_handler import MapHandler

def debug_map_loading():
    print("=== DEBUGGING MAP LOADING ===\n")
    
    try:
        # 1. Load raw JSON
        print("📂 Loading raw JSON...")
        with open("maps/map.json", "r", encoding="utf-8") as f:
            map_data = json.load(f)
        
        print(f"   ✅ JSON loaded: {len(map_data.get('features', []))} features")
        
        # 2. Test MapHandler loading
        print("\n🔧 Testing MapHandler...")
        handler = MapHandler()

        success = handler.load_map("maps/map.json")
        print(f"   Load success: {success}")
        print(f"   Zones loaded: {len(handler.zones)}")
        
        # 3. Check first few zones
        if handler.zones:
            print(f"\n📋 First 5 zones:")
            for i, (zone_id, zone) in enumerate(list(handler.zones.items())[:5]):
                print(f"   Zone {i+1}: {zone_id}")
                print(f"     Name: {zone.name}")
                print(f"     Type: {zone.zone_type}")
                print(f"     Speed: {zone.speed_limit}")
                print(f"     Geometry type: {type(zone.geometry).__name__}")
                print(f"     Active: {zone.is_active}")
                
                # Check metadata
                if hasattr(zone, 'metadata') and zone.metadata:
                    bicycle = zone.metadata.get('bicycle', 'N/A')
                    maxspeed = zone.metadata.get('maxspeed', 'N/A')
                    highway = zone.metadata.get('highway', 'N/A')
                    print(f"     Bicycle: {bicycle}, Maxspeed: {maxspeed}, Highway: {highway}")
                print()
        
        # 4. Test zone matching with actual coordinates from the map
        print("🎯 Testing zone matching with map coordinates...")
        
        # Get coordinates from first few features
        features = map_data.get('features', [])
        test_coords = []
        
        for i, feature in enumerate(features[:3]):
            geometry = feature.get('geometry', {})
            coords = geometry.get('coordinates', [])
            
            if coords:
                if geometry.get('type') == 'LineString':
                    # Use first coordinate of LineString
                    if len(coords) > 0:
                        lon, lat = coords[0]
                        test_coords.append((lat, lon, f"Feature {i+1} start"))
                elif geometry.get('type') == 'Polygon':
                    # Use first coordinate of exterior ring
                    if len(coords) > 0 and len(coords[0]) > 0:
                        lon, lat = coords[0][0]
                        test_coords.append((lat, lon, f"Feature {i+1} polygon"))
        
        for lat, lon, desc in test_coords:
            try:
                zone = handler.find_zone_at_position(lat, lon)
                if zone:
                    print(f"   ✅ {desc}: Found zone {zone.id}")
                else:
                    print(f"   ❌ {desc}: No zone found at ({lat:.6f}, {lon:.6f})")
            except Exception as e:
                print(f"   💥 {desc}: Error - {e}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_map_loading()
