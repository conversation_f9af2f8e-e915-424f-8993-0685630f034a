#!/usr/bin/env python3
"""
Debug zone parsing để tìm lỗi
"""

import sys
import os
import json
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_json_parsing():
    """Debug quá trình parse JSON thành zones"""
    
    print("=== DEBUGGING JSON → ZONES PARSING ===\n")
    
    # 1. Load JSON trực tiếp
    try:
        with open("maps/map.json", "r", encoding="utf-8") as f:
            map_data = json.load(f)
        
        print(f"✅ JSON loaded successfully")
        print(f"📊 Total features in JSON: {len(map_data.get('features', []))}")
        
        features = map_data.get("features", [])
        if not features:
            print("❌ No features found in JSON")
            return
        
        # 2. Test validation
        print(f"\n🔍 TESTING VALIDATION:")
        
        # Check structure
        has_features = "features" in map_data
        features_is_list = isinstance(map_data.get("features"), list)
        features_not_empty = len(map_data.get("features", [])) > 0
        
        print(f"   Has 'features' field: {has_features}")
        print(f"   Features is list: {features_is_list}")
        print(f"   Features not empty: {features_not_empty}")
        
        if has_features and features_is_list and features_not_empty:
            print("✅ JSON validation should PASS")
        else:
            print("❌ JSON validation would FAIL")
            return
        
        # 3. Test feature parsing
        print(f"\n🔍 TESTING FEATURE PARSING:")
        
        successful_zones = 0
        failed_zones = 0
        
        for i, feature in enumerate(features[:20]):  # Test first 20
            try:
                # Simulate zone creation
                properties = feature.get("properties", {})
                geometry_data = feature.get("geometry", {})
                
                # Basic checks
                has_type = feature.get("type") == "Feature"
                has_geometry = "geometry" in feature
                has_properties = "properties" in feature
                
                zone_id = properties.get("@id", f"zone_{i}")
                zone_name = properties.get("name", f"Zone {i}")
                
                # Check bicycle logic
                bicycle_access = properties.get("bicycle", "no")
                bicycle_allowed = bicycle_access.lower() in ["yes", "designated"]
                
                # Check speed parsing
                maxspeed = properties.get("maxspeed")
                if maxspeed:
                    try:
                        if isinstance(maxspeed, str):
                            import re
                            numbers = re.findall(r'\d+', maxspeed)
                            speed_limit = float(numbers[0]) if numbers else 30.0
                        else:
                            speed_limit = float(maxspeed)
                    except:
                        speed_limit = 30.0
                else:
                    speed_limit = 30.0 if bicycle_allowed else 0.0
                
                print(f"   Feature {i+1}: {zone_id}")
                print(f"     ✅ Type: {has_type}, Geometry: {has_geometry}, Properties: {has_properties}")
                print(f"     🚴 Bicycle: '{bicycle_access}' → Allowed: {bicycle_allowed}")
                print(f"     ⚡ Maxspeed: '{maxspeed}' → Speed: {speed_limit}")
                print(f"     🗺️  Geometry type: {geometry_data.get('type', 'N/A')}")
                
                successful_zones += 1
                
            except Exception as e:
                print(f"   ❌ Feature {i+1} FAILED: {e}")
                failed_zones += 1
        
        print(f"\n📊 PARSING RESULTS:")
        print(f"   ✅ Successful: {successful_zones}")
        print(f"   ❌ Failed: {failed_zones}")
        print(f"   📋 Expected zones: {successful_zones}")
        
        # 4. Test actual MapHandler  
        print(f"\n🔍 TESTING ACTUAL MAPHANDLER:")
        
        try:
            from enforcement.map_handler import MapHandler
            
            handler = MapHandler()
            success = handler.load_map("maps/map.json")  # Load Australia data with bicycle info
            
            print(f"   Load success: {success}")
            
            # 🔍 DEBUG: Check type of zones
            if hasattr(handler, 'zones'):
                zones_type = type(handler.zones).__name__
                print(f"   Zones type: {zones_type}")
                print(f"   Zones length: {len(handler.zones)}")
                
                # ✅ FIX: Convert dict to list properly
                if isinstance(handler.zones, dict):
                    zones_list = list(handler.zones.values())  # Get Zone objects from dict
                    print(f"   Zones as list length: {len(zones_list)}")
                elif isinstance(handler.zones, (list, tuple)):
                    zones_list = list(handler.zones)
                    print(f"   Zones as list length: {len(zones_list)}")
                elif isinstance(handler.zones, set):
                    zones_list = list(handler.zones)
                    print(f"   Zones as list length: {len(zones_list)}")
                else:
                    zones_list = []
                    print(f"   ⚠️  Unknown zones type: {zones_type}")
                
                if zones_list:
                    print(f"\n   First few zones:")
                    for i, zone in enumerate(zones_list[:5]):  # ✅ Now zones_list is a proper list
                        zone_id = getattr(zone, 'id', 'no_id')
                        zone_name = getattr(zone, 'name', 'no_name')
                        speed_limit = getattr(zone, 'speed_limit', 'no_speed')
                        is_restricted = getattr(zone, 'is_restricted', 'no_restricted')
                        
                        print(f"     Zone {i+1}: {zone_id} - {zone_name}")
                        print(f"       Speed: {speed_limit}, Restricted: {is_restricted}")
                        
                        # Check properties
                        if hasattr(zone, 'metadata') and zone.metadata:
                            bicycle = zone.metadata.get('bicycle', 'N/A')
                            maxspeed = zone.metadata.get('maxspeed', 'N/A')
                            print(f"       Bicycle: {bicycle}, Maxspeed: {maxspeed}")
                        elif hasattr(zone, 'properties') and zone.properties:
                            bicycle = zone.properties.get('bicycle', 'N/A')
                            maxspeed = zone.properties.get('maxspeed', 'N/A')
                            print(f"       Bicycle: {bicycle}, Maxspeed: {maxspeed}")
                
                # 🎯 TEST ZONE MATCHING
                print(f"\n   🎯 TESTING ZONE MATCHING:")
                
                # Test coordinates from Vietnam map.json (Ho Chi Minh City area)
                test_coords = [
                    (10.7293006, 106.6957442, "Vietnam feature start"),
                    (10.7292014, 106.695744, "Vietnam feature middle"),
                    (10.7291778, 106.695744, "Vietnam feature end")
                ]
                
                for lat, lon, desc in test_coords:
                    try:
                        zone = handler.find_zone_at_position(lat, lon)
                        if zone:
                            print(f"     ✅ {desc}: Found zone {zone.id}")
                        else:
                            print(f"     ❌ {desc}: No zone found")
                    except Exception as e:
                        print(f"     💥 {desc}: Error - {e}")
                        
            else:
                print(f"   ❌ No 'zones' attribute found")
                print(f"   Available attributes: {[attr for attr in dir(handler) if not attr.startswith('_')]}")
            
        except Exception as e:
            print(f"   ❌ MapHandler test failed: {e}")
            import traceback
            traceback.print_exc()
    
    except Exception as e:
        print(f"❌ Error loading JSON: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_json_parsing()